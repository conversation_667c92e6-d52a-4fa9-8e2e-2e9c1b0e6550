name: Semgrep

# https://semgrep.dev/docs/semgrep-ci/sample-ci-configs/#sample-github-actions-configuration-file
on:
  pull_request_target:
    branches:
      - master
  push:
    branches:
      - master
  schedule:
    # random HH:MM to avoid a load spike on GitHub Actions at 00:00
    - cron: 21 20 * * *

jobs:
  semgrep:
    name: <PERSON>an
    runs-on: ubuntu-latest
    container:
      image: returntocorp/semgrep
    if: (github.triggering_actor != 'dependabot[bot]')
    permissions:
      security-events: write
    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - run: semgrep ci --sarif > semgrep.sarif
        env:
          SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}
      - name: Upload SARIF file for GitHub Advanced Security Dashboard
        uses: github/codeql-action/upload-sarif@5f8171a638ada777af81d42b55959a643bb29017 # v3.28.12
        with:
          sarif_file: semgrep.sarif
        if: always()
