import type { Namespace } from '@/types';

export const namespace: Namespace = {
    name: '91porn',
    url: '91porn.com',
    description: `::: tip
91porn has multiple backup domains, routes use the permanent domain \`https://91porn.com\` by default. If the domain is not accessible, you can add \`?domain=<domain>\` to specify the domain to be used. If you want to specify the backup domain to \`https://0122.91p30.com\`, you can add \`?domain=0122.91p30.com\` to the end of all 91porn routes, then the route will become [\`/91porn?domain=0122.91p30.com\`](https://rsshub.app/91porn?domain=0122.91p30.com)
:::`,
    lang: 'zh-CN',
};
