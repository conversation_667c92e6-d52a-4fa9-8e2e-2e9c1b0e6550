name: 🧡 RSS Proposal
description: Submit a new RSS proposal
labels: ['RSS proposal']

body:

  - type: markdown
    attributes:
      value: |
        Please ensure the RSS proposal is not listed in [documentation](https://docs.rsshub.app/) or [issue](https://github.com/DIYgod/RSSHub/issues), website doesn't provide this kind of RSS feed, and provide all the information required by this template.
        Otherwise the issue will be closed immediately.

        We are flooded with feature requests and short-handed, please try to make it yourself, the [guide](https://docs.rsshub.app/joinus) is a good place to start. Submit a pull request when done!

  - type: dropdown
    id: category
    attributes:
      label: Category
      multiple: false
      options:
        - Social Media
        - New media
        - News
        - BBS
        - Blog
        - Programming
        - Design
        - Live
        - Multimedia
        - Picture
        - ACG
        - Application Updates
        - University
        - Forecast
        - Travel
        - Shopping
        - Gaming
        - Reading
        - Government
        - Study
        - Scientific Journal
        - Finance
        - Uncategorized
    validations:
      required: true

  - type: input
    id: site-url
    attributes:
      label: Website URL
      placeholder: https://example.com
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Website description
      placeholder: A short description of the website
    validations:
      required: true

  - type: textarea
    id: content
    attributes:
      label: What content should be included?
    validations:
      required: true

  - type: textarea
    id: info
    attributes:
      label: Additional description
      placeholder: Any additional information you want to share

  - type: checkboxes
    id: terms
    attributes:
      label: This is not a duplicated rss request
      options:
        - label: I have searched [existing issues](https://github.com/DIYgod/RSSHub/issues) and [pull requests](https://github.com/DIYgod/RSSHub/pulls) to ensure this rss proposal has not already been requested
          required: true
