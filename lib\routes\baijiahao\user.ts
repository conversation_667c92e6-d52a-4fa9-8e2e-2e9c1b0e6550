import { Route } from '@/types';

import cache from '@/utils/cache';
import got from '@/utils/got';
import { load } from 'cheerio';

import { config } from '@/config';

export const route: Route = {
    path: '/baidu/baijiahao/user/:id',
    categories: ['social-media'],
    example: '',
    parameters: { uid: '用户id' },
    features: {
        requireConfig: false,
        requirePuppeteer: false,
        antiCrawler: true,
        supportBT: false,
        supportPodcast: false,
        supportScihub: false,
    },
    name: '',
    maintainers: ['mengling'],
    handler,
};

function handler(ctx) {
    // const uid = ctx.req.param('uid');
    // const url = `https://author.baidu.com/home?from=bjh_article&app_id=${uid}`;
    // const key = `baidu-search:${url}`;
    // const items = await cache.tryGet(
    //     key,
    //     async () => {
    //         const response = (await got(url)).data;
    //         const visitedLinks = new Set();
    //         const $ = load(response);
    //         const contentLeft = $('#content_left');
    //         const containers = contentLeft.find('.c-container');
    //         return containers
    //             .toArray()
    //             .map((el) => {
    //                 const element = $(el);
    //                 const link = element.find('h3 a').first().attr('href');
    //                 if (link && !visitedLinks.has(link)) {
    //                     visitedLinks.add(link);
    //                     // const imgs = element
    //                     //     .find('img')
    //                     //     .toArray()
    //                     //     .map((_el) => $(_el).attr('src'));
    //                     const description = element.find('.c-gap-top-small [class^="content-right_"]').first().text() || element.find('.c-row').first().text() || element.find('.cos-row').first().text();
    //                     return {
    //                         title: element.find('h3').first().text(),
    //                         description,
    //                         link: element.find('h3 a').first().attr('href'),
    //                         author: element.find('.c-row .c-color-gray').first().text() || '',
    //                     };
    //                 }
    //                 return null;
    //             })
    //             .filter((e) => e?.link);
    //     },
    //     config.cache.routeExpire,
    //     false
    // );
    return {
        title: `1 - 百家号`,
        description: `1 - 百家号`,
        link: `https://author.baidu.com/home?from=bjh_article&app_id=1`,
        item: [
            {
                title: '1',
                description: '1',
                link: '1',
                author: '1',
            },
        ],
    };
}
