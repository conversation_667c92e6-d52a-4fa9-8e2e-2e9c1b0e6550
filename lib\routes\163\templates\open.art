{{ set chapterList = data.movieChapterList.length ? data.movieChapterList : data.audioChapterList; }}
{{ if chapterList }}
<div class="toc">
{{ each chapterList chapter chapterIndex }}
    <h3>第{{ chapterIndex + 1 }}章 {{ chapter.title }}</h3>
    {{ each chapter.contentList content contentIndex }}
        <h4>{{ contentIndex + 1 }} {{ content.title }}</h4>
    {{ /each }}
{{ /each }}
</div>
{{ /if }}
{{@ description }}
