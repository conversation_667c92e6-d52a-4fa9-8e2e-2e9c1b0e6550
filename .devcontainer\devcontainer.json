// For format details, see https://aka.ms/devcontainer.json. For config options, see the README at:
// https://github.com/devcontainers/images/blob/v0.3.24/src/javascript-node/.devcontainer/devcontainer.json
{
    "name": "Node.js",
    "image": "mcr.microsoft.com/devcontainers/javascript-node:22-bookworm",

    // Configure tool-specific properties.
    "customizations": {
        // Configure properties specific to VS Code.
        "vscode": {
            // Add the IDs of extensions you want installed when the container is created.
            "extensions": [
                "cweijan.vscode-database-client2",
                "dbaeumer.vscode-eslint",
                "eamodio.gitlens",
                "EditorConfig.EditorConfig",
                "esbenp.prettier-vscode",
                "SonarSource.sonarlint-vscode",
                "VASubasRaj.flashpost", // Thunder Client is paywalled in WSL/Codespaces/SSH > 2.30.0
                "ZihanLi.at-helper"
            ]
        }
    },

    // Use 'forwardPorts' to make a list of ports inside the container available locally.
    "forwardPorts": [1200, 3000],

    "portsAttributes": {
        "1200": {
            "label": "app port",
            "onAutoForward": "notify"
        },
        "3000": {
            "label": "docs port",
            "onAutoForward": "notify"
        }
    },

    "onCreateCommand": "sudo apt-get update && export DEBIAN_FRONTEND=noninteractive && sudo apt-get -y install --no-install-recommends ca-certificates fonts-liberation libasound2 libatk-bridge2.0-0 libatk1.0-0 libatspi2.0-0 libcairo2 libcups2 libdbus-1-3 libexpat1 libgbm1 libglib2.0-0 libnspr4 libnss3 libpango-1.0-0 libx11-6 libxcb1 libxcomposite1 libxdamage1 libxext6 libxfixes3 libxkbcommon0 libxrandr2 wget xdg-utils redis-server default-jre-headless && sudo apt-get autoremove -y && sudo apt-get clean -y && sudo rm -rf /var/lib/apt/lists/*",

    "updateContentCommand": "export JAVA_HOME=/usr/lib/jvm/default-java && pnpm config set store-dir ~/.local/share/pnpm/store && pnpm i && pnpm rb && pnpx rebrowser-puppeteer browsers install chrome",

    // Use 'postCreateCommand' to run commands after the container is created.
    "postCreateCommand": "pnpm i && pnpm rb && pnpx rebrowser-puppeteer browsers install chrome",

    // Disable auto start dev env since codespaces sometimes fails to attach to the terminal
    // "postAttachCommand": {
    //     "app": "pnpm i",
    //     // "docs": "pnpm -C website start"
    // },

    // Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
    "remoteUser": "node",
    "features": {
        "ghcr.io/devcontainers/features/docker-in-docker": {
            "version": "latest"
        },
        "ghcr.io/devcontainers/features/github-cli": {
            "version": "latest"
        }
    }
}
