name: Comment on Issue

on:
  issues:
    types: [opened, edited, reopened]

jobs:
  testRoute:
    name: Call maintainers
    runs-on: ubuntu-latest
    timeout-minutes: 5
    permissions:
      issues: write
    if: github.event.sender.login != 'issuehunt-oss[bot]'
    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: lts/*
          cache: 'pnpm'
      - name: Install dependencies (pnpm) # import remark-parse and unified
        run: pnpm i
      - name: Generate feedback
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { default: callMaintainer } = await import('${{ github.workspace }}/scripts/workflow/test-issue/call-maintainer.mjs')
            await callMaintainer({ github, context, core })
