name: PR - Docker build test

on:
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/docker-test.yml'
      - 'lib/**'
      - 'Dockerfile'
      - 'package.json'
      - 'pnpm-lock.yaml'
    types: [opened, reopened, synchronize, edited]
  # Please, always create a pull request instead of push to master.

concurrency:
  group: docker-test-${{ github.ref_name }}
  cancel-in-progress: true

jobs:
  test:
    name: Docker build & tests
    permissions:
      pull-requests: write
      attestations: write
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: Checkout
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0

      - name: Set up Docker Buildx  # needed by `cache-from`
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3.11.1

      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@c1e51972afc2121e065aed6d45c65596fe445f3f # v5.8.0
        with:
          images: rsshub
          flavor: latest=true

      - name: Build Docker image
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6.18.0
        with:
          context: .
          build-args: PUPPETEER_SKIP_DOWNLOAD=0  # also test bundling Chromium
          load: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          platforms: linux/amd64  # explicit
          cache-from: |
            type=registry,ref=${{ secrets.DOCKER_USERNAME }}/rsshub:chromium-bundled
            type=gha,scope=docker-release

      - name: Pull Request Labeler
        if: ${{ failure() }}
        uses: actions-cool/issues-helper@50068f49b7b2b3857270ead65e2d02e4459b022c # v3.6.2
        with:
          actions: 'add-labels'
          token: ${{ secrets.GITHUB_TOKEN }}
          issue-number: ${{ github.event.pull_request.number }}
          labels: 'auto: DO NOT merge'

      - name: Test Docker image
        run: bash scripts/docker/test-docker.sh

      - name: Export Docker image
        run: docker save rsshub:latest | zstdmt -o rsshub.tar.zst

      - name: Upload Docker image
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: docker-image
          path: rsshub.tar.zst
          retention-days: 1
