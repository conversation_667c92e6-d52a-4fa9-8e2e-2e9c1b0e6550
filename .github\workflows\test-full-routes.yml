name: Build assets (Full Routes Test Result)

on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 * * *'

jobs:
  build:
    runs-on: ubuntu-latest
    name: Build assets
    timeout-minutes: 120
    permissions:
      contents: write
    steps:
      - name: Checkout
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - name: Install pnpm
        uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
      - name: Use Node.js Active LTS
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: lts/*
          cache: 'pnpm'
      - name: Install dependencies (yarn)
        run: pnpm i
      - name: Build assets
        run: pnpm build
      - name: Build full routes test result
        continue-on-error: true
        run: pnpm vitest:fullroutes
      - name: Deploy
        uses: peaceiris/actions-gh-pages@4f9cc6602d3f66b9c108549d475ec49e8ef4d45e # v4.0.0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./assets
          user_name: 'github-actions[bot]'
          user_email: '41898282+github-actions[bot]@users.noreply.github.com'
          keep_files: true
