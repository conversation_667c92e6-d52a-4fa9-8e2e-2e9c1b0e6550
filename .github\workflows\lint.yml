name: <PERSON><PERSON>

# https://docs.github.com/en/actions/using-workflows/events-that-trigger-workflows#pull_request
# pull_request includes [opened, reopened, synchronize] events by default
# 'edited' is required for title-lint
on:
  push: {}
  pull_request:
    types: [opened, reopened, synchronize, edited]
  pull_request_target:
    types: [opened, reopened, synchronize, edited]

jobs:
  # eslint:
  #   name: ESLint
  #   if: ${{ github.event_name == 'push' || github.event_name == 'pull_request' }}
  #   runs-on: ubuntu-latest
  #   timeout-minutes: 5
  #   steps:
  #     - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
  #     - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
  #       with:
  #         version: 9
  #     - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
  #       with:
  #         node-version: lts/*
  #         cache: 'pnpm'
  #     - run: pnpm i
  #     - name: Lint
  #       run: pnpm run lint

# https://github.com/actions/starter-workflows/blob/main/code-scanning/eslint.yml
  eslint-warning:
    name: Lint
    if: ${{ github.event_name == 'push' || github.event_name == 'pull_request' }}
    runs-on: ubuntu-latest
    timeout-minutes: 5
    permissions:
      security-events: write
    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: lts/*
          cache: 'pnpm'
      - run: pnpm i
      - name: Lint
        run: pnpm run lint
          --format @microsoft/eslint-formatter-sarif
          --output-file eslint-results.sarif
        continue-on-error: true
      - name: Upload analysis results to GitHub
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: eslint-results.sarif
          wait-for-processing: true

# https://github.com/amannn/action-semantic-pull-request
  title-lint:
    if: ${{ github.event_name == 'pull_request_target' && github.repository == 'DIYgod/RSSHub' }}
    name: Validate PR title
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - uses: amannn/action-semantic-pull-request@fdd4d3ddf614fbcd8c29e4b106d3bbe0cb2c605d # v6.0.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          ignoreLabels: |
            dependencies
          wip: true

  labeler:
    name: Pull Request Labeler
    if: ${{ github.event_name == 'pull_request_target' && github.actor != 'dependabot[bot]' && github.repository == 'DIYgod/RSSHub' }}
    permissions:
      pull-requests: write
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - uses: actions/labeler@8558fd74291d67161a8a78ce36a881fa63b766a9 # v5.0.0
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
