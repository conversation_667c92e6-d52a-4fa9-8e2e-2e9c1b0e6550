name: Build assets

on:
  workflow_dispatch:
  push:
    branches:
      - master
    paths:
      - 'lib/**/*.ts'

jobs:
  build:
    runs-on: ubuntu-latest
    name: Build assets
    timeout-minutes: 5
    permissions:
      contents: write
    steps:
      - name: Checkout
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - name: Install pnpm
        uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
      - name: Use Node.js Active LTS
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: lts/*
          cache: 'pnpm'
      - name: Install dependencies (yarn)
        run: pnpm i

      # assets
      - name: Build assets
        run: pnpm build
      - name: Deploy
        uses: peaceiris/actions-gh-pages@4f9cc6602d3f66b9c108549d475ec49e8ef4d45e # v4.0.0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./assets
          user_name: 'github-actions[bot]'
          user_email: '41898282+github-actions[bot]@users.noreply.github.com'
          # prevent deleting build/test-full-routes.json which will break build:docs
          keep_files: true

      # docs
      - name: Build docs
        run: pnpm build:docs
      - id: check-docs-env
        env:
          DOCS_API_TOKEN: ${{ secrets.DOCS_API_TOKEN }}
        if: ${{ env.DOCS_API_TOKEN != '' }}
        run: echo "defined=true" >> $GITHUB_OUTPUT
      - name: Checkout docs
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        if: steps.check-docs-env.outputs.defined == 'true'
        with:
          repository: 'RSSNext/rsshub-docs'
          token: ${{ secrets.DOCS_API_TOKEN }}
          path: rsshub-docs
      - name: Update docs
        if: steps.check-docs-env.outputs.defined == 'true'
        run: |
          cp -r ./assets/build/docs/en/* ./rsshub-docs/src/routes
          cp -r ./assets/build/docs/zh/* ./rsshub-docs/src/zh/routes
          cp ./lib/types.ts ./rsshub-docs/.vitepress/theme/types.ts
          cp ./scripts/workflow/data.ts ./rsshub-docs/.vitepress/config/data.ts
      - name: Commit docs
        if: steps.check-docs-env.outputs.defined == 'true'
        run: |
          cd rsshub-docs
          git config --local user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git status
          git diff-index --quiet HEAD || (git commit -m "chore: auto build https://github.com/$GITHUB_REPOSITORY/commit/$GITHUB_SHA" -a --no-verify && git push "https://${GITHUB_ACTOR}:${{ secrets.DOCS_API_TOKEN }}@github.com/RSSNext/rsshub-docs.git" HEAD:main)

      # vercel
      - id: check-vercel-env
        env:
          VERCEL_API_TOKEN: ${{ secrets.VERCEL_API_TOKEN }}
        if: ${{ env.VERCEL_API_TOKEN != '' }}
        run: echo "defined=true" >> $GITHUB_OUTPUT
      - name: Checkout vercel
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        if: steps.check-vercel-env.outputs.defined == 'true'
        with:
          repository: 'RSSNext/rsshub-vercel'
          token: ${{ secrets.VERCEL_API_TOKEN }}
          path: rsshub-vercel
      - name: Build vercel
        if: steps.check-vercel-env.outputs.defined == 'true'
        run: pnpm build:vercel
      - name: Update vercel
        if: steps.check-vercel-env.outputs.defined == 'true'
        run: |
          rm -rf ./rsshub-vercel/src
          rm -rf ./rsshub-vercel/patches
          rm -rf ./rsshub-vercel/pnpm-lock.yaml
          mkdir -p ./rsshub-vercel/src
          mkdir -p ./rsshub-vercel/patches
          cp -r ./dist/* ./rsshub-vercel/src
          cp -r ./patches/* ./rsshub-vercel/patches
          cd rsshub-vercel
          pnpm i
      - name: Commit vercel
        if: steps.check-vercel-env.outputs.defined == 'true'
        run: |
          cd rsshub-vercel
          git config --local user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git add -A
          git status
          git diff-index --quiet HEAD || (git commit -m "chore: auto build https://github.com/$GITHUB_REPOSITORY/commit/$GITHUB_SHA" -a --no-verify && git push "https://${GITHUB_ACTOR}:${{ secrets.VERCEL_API_TOKEN }}@github.com/RSSNext/rsshub-vercel.git" HEAD:main)
