name: 'Close stale issues and PRs'

on:
  schedule:
    - cron: '31 23 * * *'

permissions:
  issues: write
  pull-requests: write

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@5bef64f19d7facfb25b37b414482c7164d639639 # v9.1.0
        with:
          # Don't stale issues
          days-before-issue-stale: -1
          days-before-pr-stale: 23
          days-before-close: 7
          stale-pr-message: >
            This PR is stale because it has been opened for more than 3 weeks with no activity. Comment or this will be closed in 7 days.
          close-issue-message: 'This issue was closed because it has been stalled for 7 days with no activity.'
          close-pr-message: 'This PR was closed because it has been stalled for 7 days with no activity.'
          exempt-issue-labels: 'dependencies,wait for upstream'
          exempt-pr-labels: 'dependencies,wait for upstream'
          any-of-issue-labels: 'more data required'
