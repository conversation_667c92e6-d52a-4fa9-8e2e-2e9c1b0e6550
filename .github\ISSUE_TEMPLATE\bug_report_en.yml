name: 🐛 Bug Report
description: Submit discovered bugs
labels: ['RSS bug']

body:
  - type: markdown
    attributes:
      value: |
        Please ensure you have read [documentation](https://docs.rsshub.app/), and provide all the information required by this template, otherwise the issue will be closed immediately.
        Due to the anti-crawling policy implemented by certain websites, some RSS routes provided by the demo will return status code 403. This is not an issue caused by RSSHub and please do not report it.

  - type: textarea
    id: routes
    attributes:
      label: Routes
      description: The involved route, without any parameters, copied directly from the docs "route" field, one link per line. Use `NOROUTE` if it is not route related.
      placeholder: /someroute/:type?
      render: routes
    validations:
      required: true

  - type: textarea
    id: fullroutes
    attributes:
      label: Full routes
      description: The involved route, with all required and optional parameters, and could be duplicate if necessary (different parameters)
      placeholder: /routes/1234?some_extension=mode
      render: fullroutes
    validations:
      required: true

  - type: input
    id: docs-link
    attributes:
      label: Related documentation
      description: Link to related documentation
      placeholder: https://docs.rsshub.app/...
    validations:
      required: true

  - type: textarea
    id: what-expected
    attributes:
      label: What is expected?
    validations:
      required: true

  - type: textarea
    id: actual-happened
    attributes:
      label: What is actually happening?
    validations:
      required: true

  - type: dropdown
    id: deployment
    attributes:
      label: Deployment information
      multiple: false
      options:
        - RSSHub demo (https://rsshub.app)
        - Self-hosted
    validations:
      required: true

  - type: input
    id: deploy-info
    attributes:
      label: Deployment information (for self-hosted)
      description: Please provide your OS, node version and docker version(if applicable)
      placeholder: 'OS: Linux, Node: v10.15.3, Docker: v19.03.13'

  - type: textarea
    id: logs
    attributes:
      label: Additional info
      description: logs, errors, etc.
      render: shell
    validations:
      required: true

  - type: checkboxes
    id: terms
    attributes:
      label: This is not a duplicated issue
      options:
        - label: I have searched [existing issues](https://github.com/DIYgod/RSSHub/issues) to ensure this bug has not already been reported
          required: true
