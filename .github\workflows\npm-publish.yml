name: npm Publish

on:
  push:
    branches:
      - master
    paths:
      - '.github/workflows/npm-publish.yml'
      - 'lib/**'

permissions:
  id-token: write

jobs:
  build:
    name: npm publish
    if: github.repository == 'DIYgod/RSSHub'
    runs-on: ubuntu-latest
    timeout-minutes: 5
    env:
      HUSKY: 0
    steps:
      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
      - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
      - uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4.4.0
        with:
          node-version: lts/*
          cache: 'pnpm'
          registry-url: 'https://registry.npmjs.org'
      - name: Install dependencies (pnpm)
        run: pnpm i
      - name: Run postinstall script for dependencies
        run: pnpm rb
      - name: Release
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          npx version-from-git --allow-same-version --template 'master.short'
      - name: Publish to npmjs
        run: pnpm publish --provenance --access public
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
